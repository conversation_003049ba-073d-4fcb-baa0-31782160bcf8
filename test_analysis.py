"""
测试分析脚本 - 测试数据输出和pandas数据拼接
"""
import pandas as pd
from loguru import logger
from main import WildberriesKeywordAnalyzer

def test_single_campaign():
    """测试单个广告活动的分析"""
    logger.info("开始测试单个广告活动分析...")
    
    try:
        # 创建分析器
        analyzer = WildberriesKeywordAnalyzer()
        
        # 检查数据库连接
        if not analyzer._check_database():
            logger.error("数据库连接失败，无法继续测试")
            return
        
        # 获取广告活动
        campaigns = analyzer._get_campaigns()
        if not campaigns:
            logger.error("没有获取到广告活动")
            return
        
        # 筛选拍卖广告
        auction_campaigns = analyzer._filter_auction_campaigns(campaigns)
        if not auction_campaigns:
            logger.error("没有找到拍卖广告")
            return
        
        # 只测试第一个广告活动
        test_campaign = auction_campaigns[0]
        logger.info(f"测试广告活动: {test_campaign.campaign_id} - {test_campaign.name}")
        
        # 使用第一个API客户端进行分析
        if not analyzer.api_manager.clients:
            logger.error("没有可用的API客户端")
            return
        
        from data_processor import KeywordAnalyzer
        keyword_analyzer = KeywordAnalyzer(analyzer.api_manager.clients[0])
        
        # 分析单个广告活动
        result_df = keyword_analyzer.analyze_campaign(test_campaign)
        
        if result_df.empty:
            logger.warning("分析结果为空")
            return
        
        logger.info("=== 分析结果 ===")
        logger.info(f"结果DataFrame形状: {result_df.shape}")
        logger.info(f"列名: {list(result_df.columns)}")
        
        # 显示前几行数据
        logger.info("=== 前5行数据 ===")
        print(result_df.head().to_string())
        
        # 显示数据类型
        logger.info("=== 数据类型 ===")
        print(result_df.dtypes)
        
        # 显示空值统计
        logger.info("=== 空值统计 ===")
        print(result_df.isnull().sum())
        
        # 保存测试结果
        result_df.to_csv("test_result_v2.csv", index=False, encoding='utf-8-sig')
        logger.info("✅ 测试结果已保存到 test_result.csv")
        
        return result_df
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主测试函数"""
    logger.info("开始数据测试...")
    
    # 测试单个广告活动
    result = test_single_campaign()
    
    if result is not None:
        logger.info("🎉 测试成功完成！")
    else:
        logger.error("❌ 测试失败")

if __name__ == "__main__":
    main()

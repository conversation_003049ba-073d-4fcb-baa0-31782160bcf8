"""
配置管理模块
"""
from typing import List
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 数据库配置
    pg_host: str = Field(..., env="PG_HOST")
    pg_port: int = Field(5432, env="PG_PORT")
    pg_user: str = Field(..., env="PG_USER")
    pg_password: str = Field(..., env="PG_PASSWORD")
    pg_db: str = Field(..., env="PG_DB")
    
    # Wildberries API配置
    wb_api_keys: str = Field(..., env="WB_API_KEYS")
    wb_adv_api_base_url: str = Field("https://advert-api.wildberries.ru", env="WB_ADV_API_BASE_URL")
    wb_analytics_api_base_url: str = Field("https://seller-analytics-api.wildberries.ru", env="WB_ANALYTICS_API_BASE_URL")
    
    # 应用配置
    log_level: str = Field("INFO", env="LOG_LEVEL")
    max_retries: int = Field(3, env="MAX_RETRIES")
    request_timeout: int = Field(30, env="REQUEST_TIMEOUT")
    data_period_days: int = Field(30, env="DATA_PERIOD_DAYS")
    api_call_interval: float = Field(1.0, env="API_CALL_INTERVAL")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
    
    @property
    def api_keys_list(self) -> List[str]:
        """获取API密钥列表"""
        return [key.strip() for key in self.wb_api_keys.split(",") if key.strip()]
    
    @property
    def database_url(self) -> str:
        """获取数据库连接URL"""
        return f"postgresql://{self.pg_user}:{self.pg_password}@{self.pg_host}:{self.pg_port}/{self.pg_db}"


# 全局配置实例
settings = Settings()

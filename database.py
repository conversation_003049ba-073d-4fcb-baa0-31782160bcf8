"""
数据库操作模块
"""
import psycopg2
from psycopg2.extras import RealDictCursor
from typing import List, Optional, Dict, Any
from contextlib import contextmanager
from loguru import logger

from config import settings
from models import SimilarityData


class DatabaseError(Exception):
    """数据库操作异常"""
    pass


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.connection_params = {
            'host': settings.pg_host,
            'port': settings.pg_port,
            'user': settings.pg_user,
            'password': settings.pg_password,
            'database': settings.pg_db
        }
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接上下文管理器"""
        conn = None
        try:
            conn = psycopg2.connect(**self.connection_params)
            yield conn
        except psycopg2.Error as e:
            logger.error(f"数据库连接错误: {e}")
            if conn:
                conn.rollback()
            raise DatabaseError(f"数据库连接错误: {e}")
        finally:
            if conn:
                conn.close()
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                    logger.info("数据库连接测试成功")
                    return True
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            return False
    
    def get_similarity_data(self, keyword: str, target_product_id: str) -> Optional[SimilarityData]:
        """获取关键词相关度数据"""
        query = """
        SELECT 
            keyword,
            target_product_id,
            avg_similarity,
            similar_count,
            competitor_count,
            valid_scores
        FROM pj_similar.product_analyze_similar_result 
        WHERE keyword = %s AND target_product_id = %s
        """
        
        try:
            with self.get_connection() as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    cursor.execute(query, (keyword, target_product_id))
                    result = cursor.fetchone()
                    
                    if result:
                        return SimilarityData(
                            keyword=result['keyword'],
                            target_product_id=result['target_product_id'],
                            avg_similarity=float(result['avg_similarity']) if result['avg_similarity'] else 0.0,
                            similar_count=int(result['similar_count']) if result['similar_count'] else 0,
                            competitor_count=int(result['competitor_count']) if result['competitor_count'] else 0,
                            valid_scores=int(result['valid_scores']) if result['valid_scores'] else 0
                        )
                    return None
                    
        except Exception as e:
            logger.error(f"获取相关度数据失败 - keyword: {keyword}, product_id: {target_product_id}, 错误: {e}")
            return None
    
    def batch_get_similarity_data(self, keyword_product_pairs: List[tuple]) -> Dict[tuple, SimilarityData]:
        """批量获取关键词相关度数据"""
        if not keyword_product_pairs:
            return {}
        
        # 构建批量查询
        placeholders = ','.join(['(%s, %s)'] * len(keyword_product_pairs))
        query = f"""
        SELECT 
            keyword,
            target_product_id,
            avg_similarity,
            similar_count,
            competitor_count,
            valid_scores
        FROM pj_similar.product_analyze_similar_result 
        WHERE (keyword, target_product_id) IN (VALUES {placeholders})
        """
        
        # 展开参数
        params = []
        for keyword, product_id in keyword_product_pairs:
            params.extend([keyword, product_id])
        
        try:
            with self.get_connection() as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    cursor.execute(query, params)
                    results = cursor.fetchall()
                    
                    # 构建结果字典
                    similarity_dict = {}
                    for result in results:
                        key = (result['keyword'], result['target_product_id'])
                        similarity_dict[key] = SimilarityData(
                            keyword=result['keyword'],
                            target_product_id=result['target_product_id'],
                            avg_similarity=float(result['avg_similarity']) if result['avg_similarity'] else 0.0,
                            similar_count=int(result['similar_count']) if result['similar_count'] else 0,
                            competitor_count=int(result['competitor_count']) if result['competitor_count'] else 0,
                            valid_scores=int(result['valid_scores']) if result['valid_scores'] else 0
                        )
                    
                    logger.info(f"批量获取相关度数据成功，查询 {len(keyword_product_pairs)} 条，返回 {len(similarity_dict)} 条")
                    return similarity_dict
                    
        except Exception as e:
            logger.error(f"批量获取相关度数据失败: {e}")
            return {}
    
    def check_table_exists(self) -> bool:
        """检查相关度数据表是否存在"""
        query = """
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'pj_similar' 
            AND table_name = 'product_analyze_similar_result'
        )
        """
        
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(query)
                    exists = cursor.fetchone()[0]
                    logger.info(f"相关度数据表存在状态: {exists}")
                    return exists
        except Exception as e:
            logger.error(f"检查表存在状态失败: {e}")
            return False
    
    def get_table_sample_data(self, limit: int = 5) -> List[Dict[str, Any]]:
        """获取表的样本数据"""
        query = """
        SELECT 
            keyword,
            target_product_id,
            avg_similarity,
            similar_count,
            competitor_count,
            valid_scores
        FROM pj_similar.product_analyze_similar_result 
        LIMIT %s
        """
        
        try:
            with self.get_connection() as conn:
                with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                    cursor.execute(query, (limit,))
                    results = cursor.fetchall()
                    return [dict(row) for row in results]
        except Exception as e:
            logger.error(f"获取样本数据失败: {e}")
            return []


# 全局数据库管理器实例
db_manager = DatabaseManager()
